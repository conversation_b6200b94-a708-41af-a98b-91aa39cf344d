"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ensurePropertySystemConfigs = ensurePropertySystemConfigs;
var client_1 = require("@prisma/client");
var prisma = new client_1.PrismaClient();
function ensurePropertySystemConfigs() {
    return __awaiter(this, void 0, void 0, function () {
        var properties, _loop_1, _i, properties_1, property, error_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    console.log('🔧 Starting property system configuration check...');
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 7, 8, 10]);
                    return [4 /*yield*/, prisma.property.findMany({
                            select: {
                                id: true,
                                name: true,
                            },
                        })];
                case 2:
                    properties = _a.sent();
                    console.log("\uD83D\uDCCB Found ".concat(properties.length, " properties to check"));
                    _loop_1 = function (property) {
                        var existingConfigs, defaultSystemConfigs, createdConfigs, existingSystemTypes_1, requiredSystems, missingSystems, missingConfigs, createdConfigs;
                        return __generator(this, function (_b) {
                            switch (_b.label) {
                                case 0:
                                    console.log("\n\uD83C\uDFE2 Checking property: ".concat(property.name, " (").concat(property.id, ")"));
                                    return [4 /*yield*/, prisma.propertySystemConfig.findMany({
                                            where: { propertyId: property.id },
                                        })];
                                case 1:
                                    existingConfigs = _b.sent();
                                    console.log("   \uD83D\uDCCA Found ".concat(existingConfigs.length, " existing system configs"));
                                    if (!(existingConfigs.length === 0)) return [3 /*break*/, 3];
                                    console.log("   \u2795 Creating default system configurations...");
                                    defaultSystemConfigs = [
                                        { systemType: client_1.SystemType.WATER, displayName: 'Water Management', displayOrder: 1 },
                                        { systemType: client_1.SystemType.ELECTRICITY, displayName: 'Electricity Management', displayOrder: 2 },
                                        { systemType: client_1.SystemType.SECURITY, displayName: 'Security Management', displayOrder: 3 },
                                        { systemType: client_1.SystemType.INTERNET, displayName: 'Internet Management', displayOrder: 4 },
                                        { systemType: client_1.SystemType.MAINTENANCE, displayName: 'Maintenance Management', displayOrder: 5 },
                                    ];
                                    return [4 /*yield*/, prisma.propertySystemConfig.createMany({
                                            data: defaultSystemConfigs.map(function (config) { return (__assign(__assign({ propertyId: property.id }, config), { isEnabled: true, configuration: {} })); }),
                                        })];
                                case 2:
                                    createdConfigs = _b.sent();
                                    console.log("   \u2705 Created ".concat(createdConfigs.count, " system configurations"));
                                    return [3 /*break*/, 6];
                                case 3:
                                    existingSystemTypes_1 = existingConfigs.map(function (config) { return config.systemType; });
                                    requiredSystems = [
                                        client_1.SystemType.WATER,
                                        client_1.SystemType.ELECTRICITY,
                                        client_1.SystemType.SECURITY,
                                        client_1.SystemType.INTERNET,
                                        client_1.SystemType.MAINTENANCE,
                                    ];
                                    missingSystems = requiredSystems.filter(function (systemType) { return !existingSystemTypes_1.includes(systemType); });
                                    if (!(missingSystems.length > 0)) return [3 /*break*/, 5];
                                    console.log("   \u2795 Adding missing systems: ".concat(missingSystems.join(', ')));
                                    missingConfigs = missingSystems.map(function (systemType, index) { return ({
                                        propertyId: property.id,
                                        systemType: systemType,
                                        displayName: getSystemDisplayName(systemType),
                                        displayOrder: existingConfigs.length + index + 1,
                                        isEnabled: true,
                                        configuration: {},
                                    }); });
                                    return [4 /*yield*/, prisma.propertySystemConfig.createMany({
                                            data: missingConfigs,
                                        })];
                                case 4:
                                    createdConfigs = _b.sent();
                                    console.log("   \u2705 Added ".concat(createdConfigs.count, " missing system configurations"));
                                    return [3 /*break*/, 6];
                                case 5:
                                    console.log("   \u2705 All required systems already exist");
                                    _b.label = 6;
                                case 6: return [2 /*return*/];
                            }
                        });
                    };
                    _i = 0, properties_1 = properties;
                    _a.label = 3;
                case 3:
                    if (!(_i < properties_1.length)) return [3 /*break*/, 6];
                    property = properties_1[_i];
                    return [5 /*yield**/, _loop_1(property)];
                case 4:
                    _a.sent();
                    _a.label = 5;
                case 5:
                    _i++;
                    return [3 /*break*/, 3];
                case 6:
                    console.log('\n🎉 Property system configuration check completed successfully!');
                    return [3 /*break*/, 10];
                case 7:
                    error_1 = _a.sent();
                    console.error('❌ Error ensuring property system configs:', error_1);
                    throw error_1;
                case 8: return [4 /*yield*/, prisma.$disconnect()];
                case 9:
                    _a.sent();
                    return [7 /*endfinally*/];
                case 10: return [2 /*return*/];
            }
        });
    });
}
function getSystemDisplayName(systemType) {
    switch (systemType) {
        case client_1.SystemType.WATER:
            return 'Water Management';
        case client_1.SystemType.ELECTRICITY:
            return 'Electricity Management';
        case client_1.SystemType.SECURITY:
            return 'Security Management';
        case client_1.SystemType.INTERNET:
            return 'Internet Management';
        case client_1.SystemType.MAINTENANCE:
            return 'Maintenance Management';
        case client_1.SystemType.OTT:
            return 'OTT Services';
        default:
            return systemType;
    }
}
// Run the script if called directly
if (require.main === module) {
    ensurePropertySystemConfigs()
        .then(function () {
        console.log('✅ Script completed successfully');
        process.exit(0);
    })
        .catch(function (error) {
        console.error('❌ Script failed:', error);
        process.exit(1);
    });
}
