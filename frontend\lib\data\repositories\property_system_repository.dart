import '../../core/services/api_client.dart';
import '../../core/utils/api_response.dart';
import '../../core/utils/logger.dart';
import '../models/system.dart';

class PropertySystemRepository {
  final ApiClient _apiClient;

  PropertySystemRepository(this._apiClient);

  // Get property system configurations
  Future<ApiResponse<List<PropertySystemConfig>>> getPropertySystemConfigs({
    required String propertyId,
  }) async {
    try {
      final response = await _apiClient.get(
        '/property-systems/$propertyId/configs',
      );

      if (response.isSuccess) {
        print('🔍 DEBUG: PropertySystemRepository - Raw response: ${response.data}');
        print('🔍 DEBUG: PropertySystemRepository - Response data type: ${response.data.runtimeType}');

        // Handle both nested and direct array responses
        final List<dynamic> data;
        if (response.data is List) {
          // Direct array response
          data = response.data as List<dynamic>;
          print('🔍 DEBUG: PropertySystemRepository - Direct array response');
        } else if (response.data is Map && response.data['data'] is List) {
          // Nested response with data field
          data = response.data['data'] as List<dynamic>;
          print('🔍 DEBUG: PropertySystemRepository - Nested response with data field');
        } else {
          data = [];
          print('🔍 DEBUG: PropertySystemRepository - Unknown response format, using empty array');
        }

        print('🔍 DEBUG: PropertySystemRepository - Data array length: ${data.length}');

        final configs = <PropertySystemConfig>[];
        for (int i = 0; i < data.length; i++) {
          try {
            print('🔍 DEBUG: PropertySystemRepository - Parsing item $i: ${data[i]}');
            final config = PropertySystemConfig.fromJson(data[i]);
            configs.add(config);
            print('🔍 DEBUG: PropertySystemRepository - Successfully parsed item $i');
          } catch (e) {
            print('❌ ERROR: PropertySystemRepository - Failed to parse item $i: $e');
            print('❌ ERROR: PropertySystemRepository - Item data: ${data[i]}');
            rethrow;
          }
        }

        return ApiResponse.success(configs);
      } else {
        return ApiResponse.error(response.message ?? 'Unknown error');
      }
    } catch (e) {
      Logger.error('Error fetching property system configs: $e');
      return ApiResponse.error('Failed to fetch property system configs: $e');
    }
  }

  // Update property system configuration
  Future<ApiResponse<PropertySystemConfig>> updatePropertySystemConfig({
    required String propertyId,
    required String configId,
    required Map<String, dynamic> data,
  }) async {
    try {
      final response = await _apiClient.put(
        '/property-systems/$propertyId/configs/$configId',
        data: data,
      );

      if (response.isSuccess) {
        final config = PropertySystemConfig.fromJson(response.data['data']);
        return ApiResponse.success(config);
      } else {
        return ApiResponse.error(response.message ?? 'Unknown error');
      }
    } catch (e) {
      Logger.error('Error updating property system config: $e');
      return ApiResponse.error('Failed to update property system config: $e');
    }
  }

  // Get system content
  Future<ApiResponse<List<SystemContent>>> getSystemContent({
    required String propertyId,
    String? systemType,
    String? contentType,
  }) async {
    try {
      final queryParams = <String, String>{};
      if (systemType != null) queryParams['systemType'] = systemType;
      if (contentType != null) queryParams['contentType'] = contentType;

      print('🔍 DEBUG: Fetching system content for propertyId: $propertyId, systemType: $systemType, contentType: $contentType');

      final response = await _apiClient.get(
        '/property-systems/$propertyId/content',
        queryParameters: queryParams,
      );

      print('🔍 DEBUG: API Response: ${response.data}');

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        print('🔍 DEBUG: Data array length: ${data.length}');

        if (data.isEmpty) {
          print('🔍 DEBUG: No system content found, returning empty list');
          return ApiResponse.success(<SystemContent>[]);
        }

        final content = <SystemContent>[];
        for (int i = 0; i < data.length; i++) {
          try {
            print('🔍 DEBUG: Parsing item $i: ${data[i]}');
            final item = SystemContent.fromJson(data[i] as Map<String, dynamic>);
            content.add(item);
            print('🔍 DEBUG: Successfully parsed item $i');
          } catch (e) {
            print('❌ ERROR: Failed to parse item $i: $e');
            print('❌ ERROR: Item data: ${data[i]}');
            // Continue with other items instead of failing completely
          }
        }

        return ApiResponse.success(content);
      } else {
        return ApiResponse.error(response.message ?? 'Unknown error');
      }
    } catch (e) {
      Logger.error('Error fetching system content: $e');
      return ApiResponse.error('Failed to fetch system content: $e');
    }
  }

  // Create system content
  Future<ApiResponse<SystemContent>> createSystemContent({
    required String propertyId,
    required Map<String, dynamic> data,
  }) async {
    try {
      final response = await _apiClient.post(
        '/property-systems/$propertyId/content',
        data: data,
      );

      if (response.isSuccess) {
        final content = SystemContent.fromJson(response.data['data']);
        return ApiResponse.success(content);
      } else {
        return ApiResponse.error(response.message ?? 'Unknown error');
      }
    } catch (e) {
      Logger.error('Error creating system content: $e');
      return ApiResponse.error('Failed to create system content: $e');
    }
  }

  // Update system content
  Future<ApiResponse<SystemContent>> updateSystemContent({
    required String propertyId,
    required String contentId,
    required Map<String, dynamic> data,
  }) async {
    try {
      final response = await _apiClient.put(
        '/property-systems/$propertyId/content/$contentId',
        data: data,
      );

      if (response.isSuccess) {
        final content = SystemContent.fromJson(response.data['data']);
        return ApiResponse.success(content);
      } else {
        return ApiResponse.error(response.message ?? 'Unknown error');
      }
    } catch (e) {
      Logger.error('Error updating system content: $e');
      return ApiResponse.error('Failed to update system content: $e');
    }
  }

  // Delete system content
  Future<ApiResponse<void>> deleteSystemContent({
    required String propertyId,
    required String contentId,
  }) async {
    try {
      final response = await _apiClient.delete(
        '/property-systems/$propertyId/content/$contentId',
      );

      if (response.isSuccess) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.message ?? 'Unknown error');
      }
    } catch (e) {
      Logger.error('Error deleting system content: $e');
      return ApiResponse.error('Failed to delete system content: $e');
    }
  }

  // Get system contacts
  Future<ApiResponse<List<SystemContact>>> getSystemContacts({
    required String propertyId,
    String? systemType,
  }) async {
    try {
      final queryParams = <String, String>{};
      if (systemType != null) queryParams['systemType'] = systemType;

      final response = await _apiClient.get(
        '/property-systems/$propertyId/contacts',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        final contacts = data.map((item) => SystemContact.fromJson(item)).toList();
        return ApiResponse.success(contacts);
      } else {
        return ApiResponse.error(response.message ?? 'Unknown error');
      }
    } catch (e) {
      Logger.error('Error fetching system contacts: $e');
      return ApiResponse.error('Failed to fetch system contacts: $e');
    }
  }

  // Create system contact
  Future<ApiResponse<SystemContact>> createSystemContact({
    required String propertyId,
    required Map<String, dynamic> data,
  }) async {
    try {
      final response = await _apiClient.post(
        '/property-systems/$propertyId/contacts',
        data: data,
      );

      if (response.isSuccess) {
        final contact = SystemContact.fromJson(response.data['data']);
        return ApiResponse.success(contact);
      } else {
        return ApiResponse.error(response.message ?? 'Unknown error');
      }
    } catch (e) {
      Logger.error('Error creating system contact: $e');
      return ApiResponse.error('Failed to create system contact: $e');
    }
  }

  // Update system contact
  Future<ApiResponse<SystemContact>> updateSystemContact({
    required String propertyId,
    required String contactId,
    required Map<String, dynamic> data,
  }) async {
    try {
      final response = await _apiClient.put(
        '/property-systems/$propertyId/contacts/$contactId',
        data: data,
      );

      if (response.isSuccess) {
        final contact = SystemContact.fromJson(response.data['data']);
        return ApiResponse.success(contact);
      } else {
        return ApiResponse.error(response.message ?? 'Unknown error');
      }
    } catch (e) {
      Logger.error('Error updating system contact: $e');
      return ApiResponse.error('Failed to update system contact: $e');
    }
  }

  // Delete system contact
  Future<ApiResponse<void>> deleteSystemContact({
    required String propertyId,
    required String contactId,
  }) async {
    try {
      final response = await _apiClient.delete(
        '/property-systems/$propertyId/contacts/$contactId',
      );

      if (response.isSuccess) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.message ?? 'Unknown error');
      }
    } catch (e) {
      Logger.error('Error deleting system contact: $e');
      return ApiResponse.error('Failed to delete system contact: $e');
    }
  }

  // Get system tabs
  Future<ApiResponse<List<SystemContent>>> getSystemTabs({
    required String propertyId,
    required String systemType,
  }) async {
    try {
      final response = await _apiClient.get(
        '/property-systems/$propertyId/tabs/$systemType',
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        final tabs = data.map((item) => SystemContent.fromJson(item)).toList();
        return ApiResponse.success(tabs);
      } else {
        return ApiResponse.error(response.message ?? 'Unknown error');
      }
    } catch (e) {
      Logger.error('Error fetching system tabs: $e');
      return ApiResponse.error('Failed to fetch system tabs: $e');
    }
  }

  // Create system tab
  Future<ApiResponse<SystemContent>> createSystemTab({
    required String propertyId,
    required Map<String, dynamic> data,
  }) async {
    try {
      final response = await _apiClient.post(
        '/property-systems/$propertyId/tabs',
        data: data,
      );

      if (response.isSuccess) {
        final tab = SystemContent.fromJson(response.data['data']);
        return ApiResponse.success(tab);
      } else {
        return ApiResponse.error(response.message ?? 'Unknown error');
      }
    } catch (e) {
      Logger.error('Error creating system tab: $e');
      return ApiResponse.error('Failed to create system tab: $e');
    }
  }

  // Update system tab
  Future<ApiResponse<SystemContent>> updateSystemTab({
    required String propertyId,
    required String tabId,
    required Map<String, dynamic> data,
  }) async {
    try {
      final response = await _apiClient.put(
        '/property-systems/$propertyId/tabs/$tabId',
        data: data,
      );

      if (response.isSuccess) {
        final tab = SystemContent.fromJson(response.data['data']);
        return ApiResponse.success(tab);
      } else {
        return ApiResponse.error(response.message ?? 'Unknown error');
      }
    } catch (e) {
      Logger.error('Error updating system tab: $e');
      return ApiResponse.error('Failed to update system tab: $e');
    }
  }

  // Delete system tab
  Future<ApiResponse<void>> deleteSystemTab({
    required String propertyId,
    required String tabId,
  }) async {
    try {
      final response = await _apiClient.delete(
        '/property-systems/$propertyId/tabs/$tabId',
      );

      if (response.isSuccess) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.message ?? 'Unknown error');
      }
    } catch (e) {
      Logger.error('Error deleting system tab: $e');
      return ApiResponse.error('Failed to delete system tab: $e');
    }
  }
}
