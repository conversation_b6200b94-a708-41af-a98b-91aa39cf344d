import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import '../../providers/system_providers.dart';
import '../../providers/property_system_providers.dart';
import 'package:url_launcher/url_launcher.dart';

import '../main/main_navigation_screen.dart';
import '../../widgets/rich_text_editor.dart';
import '../../../core/services/service_locator.dart';
import '../../../data/repositories/property_system_repository.dart';
import '../../../data/models/system.dart';
import '../../../core/utils/api_response.dart';

class WaterManagementScreen extends ConsumerStatefulWidget {
  final String propertyId;

  const WaterManagementScreen({
    super.key,
    required this.propertyId,
  });

  @override
  ConsumerState<WaterManagementScreen> createState() => _WaterManagementScreenState();
}

class _WaterManagementScreenState extends ConsumerState<WaterManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<SystemContent> _tabs = [];
  bool _isLoading = true;
  String? _error;
  final PropertySystemRepository _repository = serviceLocator.propertySystemRepository;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 0, vsync: this);
    _loadSystemTabs();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Water',
        actions: [
          IconButton(
            icon: const Icon(Icons.edit_note),
            onPressed: () => _navigateToContentManagement(),
            tooltip: 'Manage Content',
          ),
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // Handle notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.home),
            onPressed: () {
              // Navigate to home
            },
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
            const SizedBox(height: 16),
            Text(_error!, style: const TextStyle(fontSize: 16)),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadSystemTabs,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_tabs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.tab, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            const Text(
              'No tabs available for Water System',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _navigateToContentManagement,
              icon: const Icon(Icons.add),
              label: const Text('Manage Content'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Tab Bar
        Container(
          color: Colors.grey[100],
          child: TabBar(
            controller: _tabController,
            isScrollable: true,
            indicatorColor: Theme.of(context).primaryColor,
            labelColor: Theme.of(context).primaryColor,
            unselectedLabelColor: Colors.grey[600],
            tabs: _tabs.map((tab) => Tab(
              icon: Icon(_getIconData(tab.tabIcon)),
              text: tab.title,
            )).toList(),
          ),
        ),

        // Tab Bar View
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: _tabs.map((tab) => _buildDynamicTab(tab)).toList(),
          ),
        ),
      ],
    );
  }

  Future<void> _loadSystemTabs() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load tabs for this system
      final tabsResponse = await _repository.getSystemContent(
        propertyId: widget.propertyId,
        systemType: 'WATER',
        contentType: 'tab_content',
      );

      if (tabsResponse.isSuccess && tabsResponse.data != null) {
        final tabs = tabsResponse.data!.where((content) => content.isTab).toList();

        // If no tabs exist, create default tabs
        if (tabs.isEmpty) {
          await _createDefaultTabs();
          return _loadSystemTabs(); // Reload after creating defaults
        }

        // Sort tabs by display order
        tabs.sort((a, b) => (a.displayOrder ?? 0).compareTo(b.displayOrder ?? 0));

        setState(() {
          _tabs = tabs;
          _tabController.dispose();
          _tabController = TabController(length: _tabs.length, vsync: this);
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = tabsResponse.message ?? 'Failed to load system tabs';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading system tabs: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _createDefaultTabs() async {
    final defaultTabs = [
      {'title': 'Overview', 'icon': 'water_drop', 'order': 1, 'defaultContent': '# Water System Overview\n\nWelcome to the water management system for this property.\n\n## System Information\n- **Total Capacity**: 5000 liters\n- **Active Tanks**: 2 overhead tanks\n- **Pump Systems**: 2 pumps (gate & compound)\n- **Municipal Connections**: 4 connections\n\n## Current Status\n- All systems operational\n- Regular maintenance schedule active\n- Water quality monitoring in place\n\n## Quick Actions\n- View tank levels\n- Check pump status\n- Review maintenance schedule\n- Contact emergency services'},
      {'title': 'Quality Monitoring', 'icon': 'analytics', 'order': 2, 'defaultContent': '# Water Quality Monitoring\n\n## Current Parameters\n- **pH Level**: 7.2 (Normal)\n- **TDS**: 180 ppm (Good)\n- **Chlorine**: 0.5 ppm (Safe)\n- **Last Tested**: Today\n\n## Testing Schedule\n- **Daily**: Visual inspection\n- **Weekly**: pH and TDS testing\n- **Monthly**: Comprehensive analysis\n\n## Quality Standards\n- pH: 6.5 - 8.5\n- TDS: 150 - 300 ppm\n- Chlorine: 0.2 - 0.5 ppm\n\n## Actions Required\n- Weekly pH test due tomorrow\n- Monthly comprehensive test due in 2 weeks'},
      {'title': 'Maintenance', 'icon': 'build', 'order': 3, 'defaultContent': '# Water System Maintenance\n\n## Upcoming Tasks\n- **Tank Cleaning**: Due in 5 days\n- **Pump Service**: Due in 2 weeks\n- **Filter Replacement**: Due next month\n\n## Maintenance History\n- **Last Tank Cleaning**: 25 days ago (Completed)\n- **Last Pump Service**: 45 days ago (Completed)\n- **Last Filter Change**: 60 days ago (Completed)\n\n## Emergency Procedures\n1. **Water Shortage**: Switch to backup tank\n2. **Pump Failure**: Contact emergency plumber\n3. **Quality Issues**: Stop usage, test immediately\n\n## Maintenance Contacts\n- **Primary Plumber**: +91 98765 12345\n- **Tank Cleaning**: +91 98765 54321'},
      {'title': 'Contacts', 'icon': 'contact_phone', 'order': 4, 'defaultContent': '# Emergency Contacts\n\n## Municipal Services\n- **Water Board**: 1916 (24/7)\n- **Emergency Helpline**: 100\n\n## Service Providers\n- **Emergency Plumber**: +91 98765 12345\n- **Tank Cleaning Service**: +91 98765 54321\n- **Water Quality Testing**: +91 98765 67890\n\n## Property Management\n- **Property Manager**: +91 98765 11111\n- **Maintenance Supervisor**: +91 98765 22222\n\n## Important Notes\n- Keep this list updated\n- Test all numbers quarterly\n- Emergency numbers are available 24/7'},
    ];

    for (final tabData in defaultTabs) {
      await _repository.createSystemContent(
        propertyId: widget.propertyId,
        data: {
          'systemType': 'WATER',
          'contentType': 'tab_content',
          'title': tabData['title'],
          'content': {},
          'richContent': tabData['defaultContent'],
          'contentFormat': 'markdown',
          'isTab': true,
          'tabIcon': tabData['icon'],
          'displayOrder': tabData['order'],
        },
      );
    }
  }

  Widget _buildDynamicTab(SystemContent tab) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon
          Row(
            children: [
              Icon(_getIconData(tab.tabIcon), color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              Text(
                tab.title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Load content from database
          _buildDatabaseContent(tab.title),

          const SizedBox(height: 16),

          // Content Management Button
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: ElevatedButton.icon(
              onPressed: () => _navigateToContentManagement(),
              icon: const Icon(Icons.edit_note),
              label: const Text('Manage Content'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getIconData(String? iconName) {
    switch (iconName) {
      case 'water_drop': return Icons.water_drop;
      case 'analytics': return Icons.analytics;
      case 'build': return Icons.build;
      case 'contact_phone': return Icons.contact_phone;
      case 'electrical_services': return Icons.electrical_services;
      case 'power': return Icons.power;
      case 'security': return Icons.security;
      case 'videocam': return Icons.videocam;
      case 'key': return Icons.key;
      case 'report': return Icons.report;
      case 'settings': return Icons.settings;
      case 'dashboard': return Icons.dashboard;
      default: return Icons.tab;
    }
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon
          Row(
            children: [
              Icon(Icons.water_drop, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              const Text(
                'Overview',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Load content from database
          _buildDatabaseContent('Overview'),

          const SizedBox(height: 16),

          // Content Management Button
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: ElevatedButton.icon(
              onPressed: () => _navigateToContentManagement(),
              icon: const Icon(Icons.edit_note),
              label: const Text('Manage Content'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard({required String title, required String content}) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              content,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
                height: 1.4,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWaterDistributionCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Water Distribution System:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Two water pumps, one near the gate and one near the compound wall on the right side of the gate.\nTwo water tanks with a combined capacity of 5000 litres on the terrace. These tanks have two dedicated municipal water connection.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
                height: 1.4,
              ),
            ),
            const SizedBox(height: 12),
            _buildBulletPoint('Tank 1 and Tank 2 are connected with a pipe in the center.'),
            _buildBulletPoint('Tank 1 has input from the Sump near the gate. This sump gets water from 2 separate municipal connections.'),
            _buildBulletPoint('Tank 1 serves exclusively for Ground Floor and 1st Floor.'),
            _buildBulletPoint('Tank 2 serves exclusively for 2nd Floor and it doesn\'t have any water input.'),
            _buildBulletPoint('Water tanks need 3 hours to fill when completely empty'),
            _buildBulletPoint('Of the remaining 2 municipal connections, one is used for Gardening and one is used for Swimming Pool.'),
          ],
        ),
      ),
    );
  }

  Widget _buildBulletPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6, right: 8),
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[600],
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAutomationSystemCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'OVERHEAD WATER TANK AUTOMATION SYSTEM INSTALLED',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow('System Provider:', 'Ktronics Technologies Solution'),
            _buildInfoRow('Technology:', 'Solar-powered, radio signal communication'),
            const SizedBox(height: 12),
            const Text(
              'Usecase:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            _buildBulletPoint('One sensor device installed near the tank. It will have two sensors to check water levels to turn on and turn off the motor.'),
            _buildBulletPoint('The reciever device is installed near the Kitchen connected to the 1.5 HP Motor.'),
            const SizedBox(height: 12),
            const Text(
              'Trigger Mechanism',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            _buildInfoRow('Auto Start:', 'When water level reaches 20%'),
            _buildInfoRow('Auto Stop:', 'When water level reaches 90%'),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMaintenanceTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon
          Row(
            children: [
              Icon(Icons.build, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              const Text(
                'Maintenance',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Load content from database
          _buildDatabaseContent('Maintenance'),
        ],
      ),
    );
  }

  Widget _buildMaintenanceTable() {
    return Consumer(
      builder: (context, ref, child) {
        final maintenanceAsyncValue = ref.watch(waterMaintenanceProvider(widget.propertyId ?? ''));

        return maintenanceAsyncValue.when(
          data: (maintenance) {
            final maintenanceItems = maintenance.tasks.map((task) => {
              'item': task.title,
              'status': task.status,
              'frequency': task.frequency ?? 'As needed',
              'lastChecked': task.lastCompleted != null
                ? '${task.lastCompleted!.day.toString().padLeft(2, '0')}.${task.lastCompleted!.month.toString().padLeft(2, '0')}.${task.lastCompleted!.year}'
                : 'Not checked',
            }).toList();

            return _buildMaintenanceTableContent(maintenanceItems);
          },
          loading: () => _buildMaintenanceTableContent([
            {
              'item': 'Loading...',
              'status': 'Loading...',
              'frequency': 'Loading...',
              'lastChecked': 'Loading...',
            }
          ]),
          error: (error, stack) => _buildMaintenanceTableContent([
            {
              'item': 'Error loading data',
              'status': 'Error',
              'frequency': 'Error',
              'lastChecked': 'Error',
            }
          ]),
        );
      },
    );
  }

  Widget _buildMaintenanceTableContent(List<Map<String, String>> maintenanceItems) {

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Table Header
            Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: const Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      'Item',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      'Status',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      'Frequency',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      'Last Checked',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Table Rows
            ...maintenanceItems.map((item) => _buildMaintenanceRow(item)),
          ],
        ),
      ),
    );
  }

  Widget _buildMaintenanceRow(Map<String, String> item) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              item['item']!,
              style: const TextStyle(fontSize: 14),
            ),
          ),
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.green[100],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                item['status']!,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.green[700],
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          Expanded(
            child: Text(
              item['frequency']!,
              style: const TextStyle(fontSize: 14),
            ),
          ),
          Expanded(
            child: Text(
              item['lastChecked']!,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon
          Row(
            children: [
              Icon(Icons.contact_phone, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              const Text(
                'Contact Details',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Emergency contacts and service providers',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),

          const SizedBox(height: 24),

          // Contact cards will be added here
          _buildContactCard(
            'Ktronics Technologies',
            'Automation System Provider',
            '+91 98765 43210',
            '<EMAIL>',
          ),

          const SizedBox(height: 16),

          Consumer(
            builder: (context, ref, child) {
              final contactsAsyncValue = ref.watch(waterContactsProvider(widget.propertyId ?? ''));

              return contactsAsyncValue.when(
                data: (contacts) => Column(
                  children: contacts.map((contact) => Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: _buildContactCard(
                      contact.name,
                      contact.organization,
                      contact.phone,
                      contact.email,
                    ),
                  )).toList(),
                ),
                loading: () => Column(
                  children: [
                    _buildContactCard(
                      'Loading...',
                      'Loading...',
                      'Loading...',
                      'Loading...',
                    ),
                  ],
                ),
                error: (error, stack) => Column(
                  children: [
                    _buildContactCard(
                      'Municipal Water Board',
                      'Water Supply Authority',
                      '1916',
                      '<EMAIL>',
                    ),
                    const SizedBox(height: 16),
                    _buildContactCard(
                      'Emergency Plumber',
                      '24/7 Plumbing Services',
                      '+91 98765 12345',
                      '<EMAIL>',
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildContactCard(String name, String role, String phone, String email) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              name,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              role,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.phone, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  phone,
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.email, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    email,
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _makePhoneCall(phone),
                    icon: const Icon(Icons.phone, size: 16),
                    label: const Text('Call'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _sendEmail(email),
                    icon: const Icon(Icons.email, size: 16),
                    label: const Text('Email'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[200],
                      foregroundColor: Colors.grey[700],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final cleanPhone = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    final uri = Uri(scheme: 'tel', path: cleanPhone);

    try {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Could not launch phone dialer')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error making call: $e')),
        );
      }
    }
  }

  Future<void> _sendEmail(String email) async {
    final uri = Uri(scheme: 'mailto', path: email);

    try {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Could not launch email app')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error sending email: $e')),
        );
      }
    }
  }

  // NEW METHOD: Load content from API calls instead of static content
  Widget _buildDatabaseContent(String tabTitle) {
    return Consumer(
      builder: (context, ref, child) {
        final contentProvider = systemContentProvider(SystemContentParams(
          propertyId: widget.propertyId ?? '',
          systemType: 'WATER',
          contentType: 'tab_content',
        ));

        return ref.watch(contentProvider).when(
          data: (contentList) {
            // Find the content for this specific tab
            final tabContent = contentList.firstWhere(
              (content) => content.title == tabTitle,
              orElse: () => SystemContent(
                id: '',
                propertyId: widget.propertyId ?? '',
                systemType: 'WATER',
                contentType: 'tab_content',
                title: tabTitle,
                content: {},
                richContent: 'No content available for $tabTitle tab.\n\nClick "Manage Content" to add content.',
                isActive: true,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
            );

            return _buildMarkdownContent(tabContent.richContent ?? 'No content available.');
          },
          loading: () => const Center(
            child: Padding(
              padding: EdgeInsets.all(32.0),
              child: CircularProgressIndicator(),
            ),
          ),
          error: (error, stack) => Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Icon(Icons.error_outline, size: 48, color: Colors.red[300]),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading $tabTitle content',
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Please check your connection and try again.',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => ref.refresh(contentProvider),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Helper method to render markdown content
  Widget _buildMarkdownContent(String content) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      child: MarkdownBody(
        data: content,
        styleSheet: MarkdownStyleSheet(
          h1: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          h2: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          h3: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          p: TextStyle(fontSize: 14, color: Colors.grey[700], height: 1.4),
          listBullet: TextStyle(fontSize: 14, color: Colors.grey[700]),
        ),
      ),
    );
  }

  void _navigateToContentManagement() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SystemContentManagementScreen(
          propertyId: widget.propertyId,
          systemType: 'WATER',
          systemName: 'Water',
        ),
      ),
    );
  }
}

// ============================================================================
// UNIFIED SYSTEM CONTENT MANAGEMENT SCREEN
// ============================================================================
// This replaces the confusing "enhanced_system_content_management_screen.dart"
// and consolidates all content management in one place

class SystemContentManagementScreen extends StatefulWidget {
  final String propertyId;
  final String systemType;
  final String systemName;

  const SystemContentManagementScreen({
    super.key,
    required this.propertyId,
    required this.systemType,
    required this.systemName,
  });

  @override
  State<SystemContentManagementScreen> createState() => _SystemContentManagementScreenState();
}

class _SystemContentManagementScreenState extends State<SystemContentManagementScreen>
    with TickerProviderStateMixin {
  final PropertySystemRepository _repository = serviceLocator.propertySystemRepository;

  List<SystemContent> _tabs = [];
  bool _isLoading = true;
  String? _error;

  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _loadSystemTabs();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadSystemTabs() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load tabs for this system
      final tabsResponse = await _repository.getSystemContent(
        propertyId: widget.propertyId,
        systemType: widget.systemType,
        contentType: 'tab_content',
      );

      if (tabsResponse.isSuccess && tabsResponse.data != null) {
        final tabs = tabsResponse.data!.where((content) => content.isTab).toList();

        // If no tabs exist, create default tabs
        if (tabs.isEmpty) {
          await _createDefaultTabs();
          return _loadSystemTabs(); // Reload after creating defaults
        }

        setState(() {
          _tabs = tabs;
          _tabController = TabController(length: _tabs.length, vsync: this);
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = tabsResponse.message ?? 'Failed to load system tabs';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading system tabs: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _createDefaultTabs() async {
    final defaultTabs = _getDefaultTabsForSystem(widget.systemType);

    for (final tabData in defaultTabs) {
      await _repository.createSystemContent(
        propertyId: widget.propertyId,
        data: {
          'systemType': widget.systemType,
          'contentType': 'tab_content',
          'title': tabData['title'],
          'content': {},
          'richContent': tabData['defaultContent'],
          'contentFormat': 'markdown',
          'isTab': true,
          'tabIcon': tabData['icon'],
          'displayOrder': tabData['order'],
        },
      );
    }
  }

  List<Map<String, dynamic>> _getDefaultTabsForSystem(String systemType) {
    switch (systemType.toLowerCase()) {
      case 'water':
        return [
          {'title': 'Overview', 'icon': 'water_drop', 'order': 1, 'defaultContent': '# Water System Overview\n\nWelcome to the water management system.'},
          {'title': 'Quality Monitoring', 'icon': 'analytics', 'order': 2, 'defaultContent': '# Water Quality Monitoring\n\nTrack water quality parameters here.'},
          {'title': 'Maintenance', 'icon': 'build', 'order': 3, 'defaultContent': '# Water System Maintenance\n\nMaintenance schedules and procedures.'},
          {'title': 'Contacts', 'icon': 'contact_phone', 'order': 4, 'defaultContent': '# Emergency Contacts\n\nImportant contact information.'},
        ];
      case 'electricity':
        return [
          {'title': 'Overview', 'icon': 'electrical_services', 'order': 1, 'defaultContent': '# Electricity System Overview\n\nElectrical system management dashboard.'},
          {'title': 'Consumption', 'icon': 'analytics', 'order': 2, 'defaultContent': '# Power Consumption\n\nMonitor electricity usage and costs.'},
          {'title': 'Generator', 'icon': 'power', 'order': 3, 'defaultContent': '# Generator Management\n\nBackup power system information.'},
          {'title': 'Maintenance', 'icon': 'build', 'order': 4, 'defaultContent': '# Electrical Maintenance\n\nMaintenance schedules and safety procedures.'},
        ];
      case 'security':
        return [
          {'title': 'Overview', 'icon': 'security', 'order': 1, 'defaultContent': '# Security System Overview\n\nSecurity system status and controls.'},
          {'title': 'CCTV', 'icon': 'videocam', 'order': 2, 'defaultContent': '# CCTV Monitoring\n\nCamera feeds and recording management.'},
          {'title': 'Access Control', 'icon': 'key', 'order': 3, 'defaultContent': '# Access Control\n\nDoor locks and access management.'},
          {'title': 'Incidents', 'icon': 'report', 'order': 4, 'defaultContent': '# Security Incidents\n\nIncident reporting and tracking.'},
        ];
      default:
        return [
          {'title': 'Overview', 'icon': 'dashboard', 'order': 1, 'defaultContent': '# System Overview\n\nSystem management dashboard.'},
          {'title': 'Configuration', 'icon': 'settings', 'order': 2, 'defaultContent': '# Configuration\n\nSystem configuration and settings.'},
          {'title': 'Maintenance', 'icon': 'build', 'order': 3, 'defaultContent': '# Maintenance\n\nMaintenance information and schedules.'},
        ];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.systemName} Content Management'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add_box),
            onPressed: () => _showAddTabDialog(),
            tooltip: 'Add New Tab',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSystemTabs,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
            const SizedBox(height: 16),
            Text(_error!, style: const TextStyle(fontSize: 16)),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadSystemTabs,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_tabs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.tab, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No tabs available for ${widget.systemName}',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _showAddTabDialog(),
              icon: const Icon(Icons.add),
              label: const Text('Add First Tab'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Tab Bar
        Container(
          color: Colors.grey[100],
          child: TabBar(
            controller: _tabController,
            isScrollable: true,
            indicatorColor: Colors.blue[700],
            labelColor: Colors.blue[700],
            unselectedLabelColor: Colors.grey[600],
            tabs: _tabs.map((tab) => Tab(
              icon: Icon(_getIconData(tab.tabIcon)),
              text: tab.title,
            )).toList(),
          ),
        ),

        // Tab Content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: _tabs.map((tab) => _buildTabContent(tab)).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildTabContent(SystemContent tab) {
    print('🔍 DEBUG: Building tab content for "${tab.title}"');
    print('🔍 DEBUG: Tab richContent length: ${tab.richContent?.length ?? 0}');
    print('🔍 DEBUG: Tab richContent preview: ${tab.richContent?.substring(0, tab.richContent!.length > 100 ? 100 : tab.richContent!.length) ?? 'null'}...');

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Tab actions
          Row(
            children: [
              Text(
                tab.title,
                style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.edit),
                onPressed: () => _showEditTabDialog(tab),
                tooltip: 'Edit Tab',
              ),
              IconButton(
                icon: const Icon(Icons.delete),
                onPressed: () => _showDeleteTabConfirmation(tab),
                tooltip: 'Delete Tab',
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Rich Text Editor with Save Button
          Expanded(
            child: RichTextEditor(
              key: ValueKey('${tab.id}_${tab.richContent?.hashCode}'), // Force rebuild when content changes
              initialContent: tab.richContent ?? '',
              format: tab.contentFormat == 'html' ? ContentFormat.html : ContentFormat.markdown,
              onContentChanged: (content) => _saveTabContent(tab, content),
              onManualSave: () => _manualSaveTabContent(tab),
              placeholder: 'Enter content for ${tab.title} tab...',
              enableAutoSave: true, // Enable auto-save
            ),
          ),
        ],
      ),
    );
  }

  IconData _getIconData(String? iconName) {
    switch (iconName) {
      case 'water_drop': return Icons.water_drop;
      case 'analytics': return Icons.analytics;
      case 'build': return Icons.build;
      case 'contact_phone': return Icons.contact_phone;
      case 'electrical_services': return Icons.electrical_services;
      case 'power': return Icons.power;
      case 'security': return Icons.security;
      case 'videocam': return Icons.videocam;
      case 'key': return Icons.key;
      case 'report': return Icons.report;
      case 'settings': return Icons.settings;
      case 'dashboard': return Icons.dashboard;
      default: return Icons.tab;
    }
  }

  Future<void> _saveTabContent(SystemContent tab, String content) async {
    try {
      await _repository.updateSystemContent(
        propertyId: widget.propertyId,
        contentId: tab.id,
        data: {
          'richContent': content,
          'contentFormat': tab.contentFormat ?? 'markdown',
        },
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving content: $e')),
        );
      }
    }
  }

  Future<void> _manualSaveTabContent(SystemContent tab) async {
    try {
      // Get current content from the editor
      final currentContent = _getCurrentTabContent(tab);

      await _repository.updateSystemContent(
        propertyId: widget.propertyId,
        contentId: tab.id,
        data: {
          'richContent': currentContent,
          'contentFormat': tab.contentFormat ?? 'markdown',
        },
      );

      // Refresh the tab data to reflect changes in display
      _loadSystemTabs();

    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving content: $e')),
        );
      }
    }
  }

  String _getCurrentTabContent(SystemContent tab) {
    // This would ideally get the current content from the editor
    // For now, return the existing content
    return tab.richContent ?? '';
  }

  void _showAddTabDialog() {
    showDialog(
      context: context,
      builder: (context) => TabEditDialog(
        propertyId: widget.propertyId,
        systemType: widget.systemType,
        onSaved: () {
          Navigator.of(context).pop();
          _loadSystemTabs();
        },
      ),
    );
  }

  void _showEditTabDialog(SystemContent tab) {
    showDialog(
      context: context,
      builder: (context) => TabEditDialog(
        propertyId: widget.propertyId,
        systemType: widget.systemType,
        tab: tab,
        onSaved: () {
          Navigator.of(context).pop();
          _loadSystemTabs();
        },
      ),
    );
  }

  void _showDeleteTabConfirmation(SystemContent tab) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Tab'),
        content: Text('Are you sure you want to delete the "${tab.title}" tab? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _deleteTab(tab);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteTab(SystemContent tab) async {
    try {
      final response = await _repository.deleteSystemContent(
        propertyId: widget.propertyId,
        contentId: tab.id,
      );

      if (response.isSuccess) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Tab deleted successfully')),
          );
        }
        _loadSystemTabs();
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to delete tab: ${response.message}')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting tab: $e')),
        );
      }
    }
  }
}

// ============================================================================
// TAB EDIT DIALOG
// ============================================================================

class TabEditDialog extends StatefulWidget {
  final String propertyId;
  final String systemType;
  final SystemContent? tab;
  final VoidCallback onSaved;

  const TabEditDialog({
    super.key,
    required this.propertyId,
    required this.systemType,
    this.tab,
    required this.onSaved,
  });

  @override
  State<TabEditDialog> createState() => _TabEditDialogState();
}

class _TabEditDialogState extends State<TabEditDialog> {
  final _formKey = GlobalKey<FormState>();
  final PropertySystemRepository _repository = serviceLocator.propertySystemRepository;

  late TextEditingController _titleController;
  late String _selectedIcon;
  late String _selectedFormat;
  bool _isLoading = false;

  final List<Map<String, dynamic>> _availableIcons = [
    {'name': 'Dashboard', 'value': 'dashboard', 'icon': Icons.dashboard},
    {'name': 'Analytics', 'value': 'analytics', 'icon': Icons.analytics},
    {'name': 'Build', 'value': 'build', 'icon': Icons.build},
    {'name': 'Contact Phone', 'value': 'contact_phone', 'icon': Icons.contact_phone},
    {'name': 'Settings', 'value': 'settings', 'icon': Icons.settings},
    {'name': 'Security', 'value': 'security', 'icon': Icons.security},
    {'name': 'Water Drop', 'value': 'water_drop', 'icon': Icons.water_drop},
    {'name': 'Electrical Services', 'value': 'electrical_services', 'icon': Icons.electrical_services},
    {'name': 'Power', 'value': 'power', 'icon': Icons.power},
    {'name': 'Videocam', 'value': 'videocam', 'icon': Icons.videocam},
    {'name': 'Key', 'value': 'key', 'icon': Icons.key},
    {'name': 'Report', 'value': 'report', 'icon': Icons.report},
  ];

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.tab?.title ?? '');
    _selectedIcon = widget.tab?.tabIcon ?? 'dashboard';
    _selectedFormat = widget.tab?.contentFormat ?? 'markdown';
  }

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 400,
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.tab == null ? 'Add New Tab' : 'Edit Tab',
                style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 24),

              // Title Field
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'Tab Title',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a tab title';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Icon Selector
              DropdownButtonFormField<String>(
                value: _selectedIcon,
                decoration: const InputDecoration(
                  labelText: 'Tab Icon',
                  border: OutlineInputBorder(),
                ),
                items: _availableIcons.map((iconData) {
                  return DropdownMenuItem<String>(
                    value: iconData['value'],
                    child: Row(
                      children: [
                        Icon(iconData['icon']),
                        const SizedBox(width: 8),
                        Text(iconData['name']),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedIcon = value!;
                  });
                },
              ),
              const SizedBox(height: 16),

              // Content Format Selector
              DropdownButtonFormField<String>(
                value: _selectedFormat,
                decoration: const InputDecoration(
                  labelText: 'Content Format',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: 'markdown', child: Text('Markdown')),
                  DropdownMenuItem(value: 'html', child: Text('HTML')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedFormat = value!;
                  });
                },
              ),
              const SizedBox(height: 24),

              // Action Buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _saveTab,
                    child: _isLoading
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Text(widget.tab == null ? 'Add' : 'Save'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _saveTab() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final tabData = {
        'systemType': widget.systemType,
        'contentType': 'tab_content',
        'title': _titleController.text,
        'content': {},
        'richContent': widget.tab?.richContent ?? '# ${_titleController.text}\n\nWelcome to the ${_titleController.text} tab.',
        'contentFormat': _selectedFormat,
        'isTab': true,
        'tabIcon': _selectedIcon,
        'displayOrder': widget.tab?.displayOrder ?? 1,
      };

      ApiResponse response;
      if (widget.tab == null) {
        response = await _repository.createSystemContent(
          propertyId: widget.propertyId,
          data: tabData,
        );
      } else {
        response = await _repository.updateSystemContent(
          propertyId: widget.propertyId,
          contentId: widget.tab!.id,
          data: tabData,
        );
      }

      if (response.isSuccess) {
        widget.onSaved();
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error: ${response.message}')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving tab: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
