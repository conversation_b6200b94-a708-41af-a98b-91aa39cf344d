import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../data/repositories/property_repository.dart' show PropertyDetail;
import '../../providers/properties_providers.dart';
// Removed enhanced_system_content_management_screen import - content editing should be done in system screens
import '../../../data/models/system.dart';
import '../../providers/property_system_providers.dart';

class PropertySettingsScreen extends ConsumerStatefulWidget {
  final String propertyId;
  
  const PropertySettingsScreen({
    super.key,
    required this.propertyId,
  });

  @override
  ConsumerState<PropertySettingsScreen> createState() => _PropertySettingsScreenState();
}

class _PropertySettingsScreenState extends ConsumerState<PropertySettingsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final propertyDetailAsync = ref.watch(propertyDetailProvider(widget.propertyId));

    return Scaffold(
      appBar: AppBar(
        title: propertyDetailAsync.when(
          data: (property) => Text('${property?.name ?? 'Property'} Settings'),
          loading: () => const Text('Property Settings'),
          error: (_, __) => const Text('Property Settings'),
        ),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Systems'),
            Tab(icon: Icon(Icons.edit_note), text: 'Content'),
            Tab(icon: Icon(Icons.settings), text: 'Config'),
            Tab(icon: Icon(Icons.security), text: 'Access'),
          ],
        ),
      ),
      body: propertyDetailAsync.when(
        data: (property) {
          if (property == null) {
            return _buildPropertyNotFound();
          }
          return TabBarView(
            controller: _tabController,
            children: [
              _buildSystemsTab(property),
              _buildContentTab(property),
              _buildConfigTab(property),
              _buildAccessTab(property),
            ],
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => _buildErrorState(error),
      ),
    );
  }

  Widget _buildPropertyNotFound() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Property not found',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => context.pop(),
            icon: const Icon(Icons.arrow_back),
            label: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading property settings',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.red[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => context.pop(),
            icon: const Icon(Icons.arrow_back),
            label: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemsTab(PropertyDetail property) {
    final systemConfigsAsync = ref.watch(propertySystemConfigsProvider(widget.propertyId));

    return systemConfigsAsync.when(
      data: (configs) => _buildSystemsList(configs),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, color: Colors.red[400], size: 48),
            const SizedBox(height: 16),
            Text('Error loading systems: $error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.invalidate(propertySystemConfigsProvider(widget.propertyId)),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemsList(List<PropertySystemConfig> configs) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: configs.length,
      itemBuilder: (context, index) {
        final config = configs[index];
        return _buildSystemCard(config);
      },
    );
  }

  Widget _buildSystemCard(PropertySystemConfig config) {
    IconData systemIcon;
    switch (config.systemType) {
      case 'WATER':
        systemIcon = Icons.water_drop;
        break;
      case 'ELECTRICITY':
        systemIcon = Icons.electrical_services;
        break;
      case 'SECURITY':
        systemIcon = Icons.security;
        break;
      case 'INTERNET':
        systemIcon = Icons.wifi;
        break;
      case 'OTT':
        systemIcon = Icons.tv;
        break;
      case 'MAINTENANCE':
        systemIcon = Icons.build;
        break;
      default:
        systemIcon = Icons.settings;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(systemIcon, color: Colors.blue[600]),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    config.displayName ?? config.systemType,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Switch(
                  value: config.isEnabled,
                  onChanged: (value) => _toggleSystemEnabled(config, value),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _editSystemConfig(config),
                    icon: const Icon(Icons.edit),
                    label: const Text('Configure'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _navigateToSystemScreen(config),
                    icon: const Icon(Icons.open_in_new),
                    label: const Text('Open System'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentTab(PropertyDetail property) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.info_outline, size: 64, color: Colors.blue[400]),
          const SizedBox(height: 16),
          Text(
            'Content Management',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Content editing is available directly in each system screen.\nUse the "Open System" button in the Systems tab to access content management.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              // Switch to Systems tab
              _tabController.animateTo(0);
            },
            icon: const Icon(Icons.dashboard),
            label: const Text('Go to Systems'),
          ),
        ],
      ),
    );
  }

  Widget _buildConfigTab(PropertyDetail property) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Property Configuration',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          
          // Basic Information
          _buildConfigSection(
            'Basic Information',
            [
              _buildConfigItem('Property Name', property.name, Icons.business),
              _buildConfigItem('Address', property.address, Icons.location_on),
              _buildConfigItem('Type', property.type, Icons.category),
              _buildConfigItem('Status', property.status, Icons.info),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Notification Settings
          _buildConfigSection(
            'Notifications',
            [
              _buildSwitchItem('Email Alerts', true, Icons.email),
              _buildSwitchItem('SMS Notifications', false, Icons.sms),
              _buildSwitchItem('Push Notifications', true, Icons.notifications),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Monitoring Settings
          _buildConfigSection(
            'Monitoring',
            [
              _buildConfigItem('Check Interval', '5 minutes', Icons.timer),
              _buildConfigItem('Alert Threshold', '85%', Icons.warning),
              _buildConfigItem('Maintenance Window', '2:00 AM - 4:00 AM', Icons.schedule),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAccessTab(PropertyDetail property) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Access Control',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          
          // Assigned Users
          _buildConfigSection(
            'Assigned Users',
            [
              _buildUserItem('John Doe', 'Property Manager', Icons.person),
              _buildUserItem('Jane Smith', 'Maintenance Staff', Icons.build),
              _buildUserItem('Mike Johnson', 'Security Personnel', Icons.security),
            ],
          ),
          
          const SizedBox(height: 16),
          
          ElevatedButton.icon(
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('User assignment feature coming soon'),
                  backgroundColor: Colors.blue,
                ),
              );
            },
            icon: const Icon(Icons.person_add),
            label: const Text('Assign Users'),
          ),
        ],
      ),
    );
  }

  Widget _buildConfigSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Colors.blue[700],
          ),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildConfigItem(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSwitchItem(String label, bool value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          Switch(
            value: value,
            onChanged: (newValue) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('$label ${newValue ? 'enabled' : 'disabled'}'),
                  backgroundColor: newValue ? Colors.green : Colors.orange,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildUserItem(String name, String role, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  role,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Edit access for $name'),
                  backgroundColor: Colors.blue,
                ),
              );
            },
            icon: const Icon(Icons.edit, size: 20),
          ),
        ],
      ),
    );
  }

  void _toggleSystemEnabled(PropertySystemConfig config, bool enabled) async {
    try {
      // TODO: Implement actual API call to update system status
      // For now, just show a success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${config.displayName ?? config.systemType} ${enabled ? 'enabled' : 'disabled'}'),
          backgroundColor: enabled ? Colors.green : Colors.orange,
          action: SnackBarAction(
            label: 'Undo',
            onPressed: () {
              // TODO: Implement undo functionality
            },
          ),
        ),
      );

      // Refresh the data to reflect changes
      ref.invalidate(propertySystemConfigsProvider(widget.propertyId));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to update ${config.systemType}: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _editSystemConfig(PropertySystemConfig config) {
    showDialog(
      context: context,
      builder: (context) => _SystemConfigDialog(
        config: config,
        onSave: (updatedConfig) {
          // TODO: Implement actual save functionality
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${config.systemType} configuration updated'),
              backgroundColor: Colors.green,
            ),
          );
        },
      ),
    );
  }

  void _navigateToSystemScreen(PropertySystemConfig config) {
    // Navigate to the actual system screen where content management is available
    final systemRoute = _getSystemRoute(config.systemType);
    if (systemRoute != null) {
      final fullRoute = '/properties/${widget.propertyId}/$systemRoute';
      print('🔍 DEBUG: Navigating to system screen: $fullRoute');
      context.push(fullRoute);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${config.systemType} system screen not available yet'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  String? _getSystemRoute(String systemType) {
    switch (systemType.toUpperCase()) {
      case 'WATER':
        return 'water';
      case 'ELECTRICITY':
        return 'electricity';
      case 'SECURITY':
        return 'security';
      case 'INTERNET':
        return 'internet';
      case 'OTT':
        return 'ott';
      case 'MAINTENANCE':
        return 'maintenance';
      default:
        print('🔍 DEBUG: Unknown system type: $systemType');
        return null;
    }
  }
}

class _SystemConfigDialog extends StatefulWidget {
  final PropertySystemConfig config;
  final Function(PropertySystemConfig) onSave;

  const _SystemConfigDialog({
    required this.config,
    required this.onSave,
  });

  @override
  State<_SystemConfigDialog> createState() => _SystemConfigDialogState();
}

class _SystemConfigDialogState extends State<_SystemConfigDialog> {
  late TextEditingController _displayNameController;
  late bool _isEnabled;
  late int _displayOrder;

  @override
  void initState() {
    super.initState();
    _displayNameController = TextEditingController(text: widget.config.displayName);
    _isEnabled = widget.config.isEnabled;
    _displayOrder = widget.config.displayOrder ?? 1;
  }

  @override
  void dispose() {
    _displayNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Configure ${widget.config.systemType}'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Display Name
            TextField(
              controller: _displayNameController,
              decoration: const InputDecoration(
                labelText: 'Display Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),

            // Enable/Disable
            SwitchListTile(
              title: const Text('Enable System'),
              subtitle: const Text('Allow this system to be used in the property'),
              value: _isEnabled,
              onChanged: (value) {
                setState(() {
                  _isEnabled = value;
                });
              },
            ),
            const SizedBox(height: 16),

            // Display Order
            TextField(
              decoration: const InputDecoration(
                labelText: 'Display Order',
                border: OutlineInputBorder(),
                helperText: 'Order in which this system appears (1-10)',
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                _displayOrder = int.tryParse(value) ?? 1;
              },
              controller: TextEditingController(text: _displayOrder.toString()),
            ),
            const SizedBox(height: 16),

            // Configuration Preview
            if (widget.config.configuration != null && widget.config.configuration!.isNotEmpty)
              ExpansionTile(
                title: const Text('Advanced Configuration'),
                children: [
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _formatConfiguration(widget.config.configuration!),
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _saveConfiguration,
          child: const Text('Save'),
        ),
      ],
    );
  }

  String _formatConfiguration(Map<String, dynamic> config) {
    try {
      return config.entries
          .map((e) => '${e.key}: ${e.value}')
          .join('\n');
    } catch (e) {
      return 'Configuration data available';
    }
  }

  void _saveConfiguration() {
    // Create updated config (for now, just close dialog)
    // TODO: Implement actual save to API
    Navigator.of(context).pop();
    widget.onSave(widget.config);
  }
}
